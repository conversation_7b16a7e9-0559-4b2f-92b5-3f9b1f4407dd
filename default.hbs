<!DOCTYPE html>
<html lang="{{@site.locale}}">

<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-PD8BCJHQ');</script>
    <!-- End Google Tag Manager -->

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{meta_title}} - Avatar Minecraft Server</title>

    <meta name="description" content="Join The Last BlockBender, an Avatar-themed Minecraft server. Master bending abilities, engage in PvP battles, and explore custom worlds. Play now!">
    <meta name="keywords" content="Avatar Minecraft server, Minecraft bending, Airbender Minecraft, Firebender Minecraft, Waterbender Minecraft, Earthbender Minecraft, Minecraft PvP, Minecraft roleplay server, Minecraft custom plugins, Minecraft duels, Avatar-inspired Minecraft, bending abilities Minecraft, Agni Kai Minecraft, Minecraft quests, Minecraft adventure server, The Last Blockbender server">
    <link rel="stylesheet" href="{{asset "built/screen.css"}}">
    <link href="https://owlcarousel2.github.io/OwlCarousel2/assets/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="https://owlcarousel2.github.io/OwlCarousel2/assets/owlcarousel/assets/owl.theme.default.min.css" rel="stylesheet">

    {{ghost_head}}
</head>

<body class="{{#page}}{{#has tag='#darkMode'}}darkMode {{/has}}{{#has tag='#wideTemplate'}}wideTemplate {{/has}}{{/page}}{{#post}}{{#has tag='#darkMode'}}darkMode {{/has}}{{#has tag='#wideTemplate'}}wideTemplate {{/has}}{{/post}}{{body_class}}{{block "body_class"}}  is-head-{{#match @custom.navigation_layout "Logo on the left"}}left-logo{{else match @custom.navigation_layout "Logo in the middle"}}middle-logo{{else}}stacked{{/match}}{{#match @custom.title_font "=" "Elegant serif"}} has-serif-title{{/match}}{{#match @custom.body_font "=" "Elegant serif"}} has-serif-body{{/match}}">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PD8BCJHQ"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<div class="site">

    <header id="gh-head" class="gh-head gh-outer">
        <div class="gh-head-inner gh-inner">
            <div class="gh-head-brand">
                <div class="gh-head-brand-wrapper">
                    <a class="gh-head-logo" href="{{@site.url}}">
                        {{#if @site.logo}}
                            <img src="{{@site.logo}}" alt="{{@site.title}}">
                        {{else}}
                            {{@site.title}}
                        {{/if}}
                    </a>
                </div>
               
                <button class="gh-burger" aria-label="Toggle menu"></button>
            </div>

            <nav class="gh-head-menu">
                {{navigation}}
                {{#unless @site.members_enabled}}
                    {{#match @custom.navigation_layout "Stacked"}}
             
                    {{/match}}
                {{/unless}}
            </nav>

            <div class="gh-head-actions">
                {{#unless @site.members_enabled}}
                    {{^match @custom.navigation_layout "Stacked"}}
                        {{> "page-search"}}
                    {{/match}}
                {{else}}
      
                    <div class="gh-head-members">
                        {{#unless @member}}
                            {{#unless @site.members_invite_only}}
                                <a class="gh-head-link" href="#/portal/signin" data-portal="signin">Sign in</a>
                                <a class="gh-head-btn gh-btn gh-primary-btn" href="#/portal/signup" data-portal="signup">Subscribe</a>
                            {{else}}
                                <a class="gh-head-btn gh-btn gh-primary-btn" href="#/portal/signin" data-portal="signin">Sign in</a>
                            {{/unless}}
                        {{else}}
                            <a class="gh-head-btn gh-btn gh-primary-btn" href="#/portal/account" data-portal="account">Account</a>
                        {{/unless}}
                    </div>
                {{/unless}}
            </div>
        </div>
    </header>

    {{!--
    {{#is "home"}}
    <div class="site-cover{{#unless @site.cover_image}} no-image{{/unless}} u-overlay">

        {{#if @site.cover_image}}
            <img class="cover-image jarallax-img u-object-fit" src="{{img_url @site.cover_image}}" alt="{{@site.title}}">
        {{/if}}

        <div class="cover-content">
            {{#if @site.description}}
                <div class="cover-description">{{@site.description}}</div>
            {{/if}}
            <div class="search">
                <form class="search-form">
                    <div class="form-wrapper">
                        <div class="search-field" data-ghost-search>Search posts, tags, authors...</div>
                        <button class="form-button search-button" type="submit" data-ghost-search>
                            {{> "icons/search"}}
                            {{> "icons/close"}}
                        </button>
                    </div>
                </form>
                <div class="search-result"></div>
            </div>
        </div>

    </div>
    {{/is}}
    --}}

    <div class="site-content">
        {{{body}}}
    </div>

    <footer class="gh-foot{{#unless @site.secondary_navigation}} no-menu{{/unless}} gh-outer">
        <div class="gh-foot-inner gh-inner">
            
            {{#if @site.secondary_navigation}}
                <nav class="gh-foot-menu">
                    {{navigation type="secondary"}}
                </nav>
            {{/if}}
            <div class="gh-copyright">
                {{@site.title}} © {{date format="YYYY"}}
            </div>
        </div>

            <div class="footer-info">
                <p>Join the Last Block Bender — An Avatar themed Bending Minecraft Server</p>

                <p>Step into the world of bending and adventure on The Last Blockbender, the ultimate Avatar-inspired
                    Minecraft server! Choose your element — Air, Water, Earth, or Fire—and master powerful bending
                    abilities as you explore custom worlds, battle in epic PvP arenas, and complete quests to rise
                    through the ranks.</p>

                <p>Features:</p>

                <ul>
                    <li>Fully customized bending system with unique abilities and combos</li>

                    <li>PvP arenas, duels, and faction battles for competitive players</li>

                    <li>Creative server where you can build and use WorldEdit</li>

                    <li>Friendly, active community with events and competitions</li>
                </ul>

                <p>Join the over 100,000 benders from around the world and experience Minecraft like never before!
                    Whether you’re here to master your element, fight in Agni Kai duels, or just explore, The Last
                    Blockbender has something for every player.</p>
            </div>
    </footer>

</div>

{{#is "post, page"}}
    {{> "pswp"}}
{{/is}}

<script
    src="https://code.jquery.com/jquery-3.3.1.min.js"
    integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8="
    crossorigin="anonymous">
</script>
<script src="{{asset "built/main.min.js"}}"></script>

{{ghost_foot}}

</body>
</html>