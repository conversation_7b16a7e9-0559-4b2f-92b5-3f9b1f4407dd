{"name": "tlb-theme", "description": "Ghost theme for TheLastBlockBender", "version": "1.0.0", "private": true, "engines": {"ghost": ">=5.0.0"}, "license": "MIT", "author": {"name": "Ghost Foundation", "email": "<EMAIL>", "url": "https://ghost.org"}, "keywords": ["ghost", "theme", "ghost-theme"], "docs": "https://ease.ghost.io/about/", "config": {"posts_per_page": 10, "image_sizes": {"xs": {"width": 150}, "s": {"width": 400}, "m": {"width": 750}, "l": {"width": 960}, "xl": {"width": 1140}, "xxl": {"width": 1920}}, "card_assets": true, "custom": {"navigation_layout": {"type": "select", "options": ["Logo on the left", "Logo in the middle", "Stacked"], "default": "Logo on the left"}, "title_font": {"type": "select", "options": ["Modern sans-serif", "Elegant serif"], "default": "Modern sans-serif"}, "body_font": {"type": "select", "options": ["Modern sans-serif", "Elegant serif"], "default": "Modern sans-serif"}, "show_featured_posts": {"type": "boolean", "default": true, "group": "homepage"}}}, "scripts": {"dev": "gulp", "test": "gscan .", "zip": "gulp zip"}, "devDependencies": {"@tryghost/shared-theme-assets": "2.5.0", "autoprefixer": "10.4.21", "beeper": "2.1.0", "cssnano": "7.1.0", "gscan": "4.49.7", "gulp": "^5.0.1", "gulp-concat": "2.6.1", "gulp-livereload": "4.0.2", "gulp-postcss": "10.0.0", "gulp-uglify": "3.0.2", "gulp-zip": "5.1.0", "ordered-read-streams": "2.0.0", "postcss": "8.5.6", "postcss-easy-import": "4.0.0", "pump": "3.0.3"}}