<button class="gh-search gh-icon-btn page-search-trigger" aria-label="Search pages">
    {{> "icons/search"}}
</button>

<!-- Search Modal -->
<div class="page-search-modal" style="display: none;">
    <div class="page-search-modal-backdrop"></div>
    <div class="page-search-modal-content">
        <div class="page-search-modal-header">
            <form class="page-search-form">
                <div class="form-wrapper">
                    <input type="text" class="page-search-field" placeholder="Search pages..." autocomplete="off" autofocus>
                    <button class="page-search-button" type="submit">
                        {{> "icons/search"}}
                    </button>
                </div>
            </form>
            <button class="page-search-close" aria-label="Close search">
                {{> "icons/close"}}
            </button>
        </div>
        <div class="page-search-results"></div>
    </div>
</div>

<script>
function initPageSearch() {
    const searchTrigger = document.querySelector('.page-search-trigger');
    const searchModal = document.querySelector('.page-search-modal');
    const searchForm = document.querySelector('.page-search-form');
    const searchField = document.querySelector('.page-search-field');
    const searchResults = document.querySelector('.page-search-results');
    const searchClose = document.querySelector('.page-search-close');
    const modalBackdrop = document.querySelector('.page-search-modal-backdrop');
    
    // Exit if elements don't exist
    if (!searchTrigger || !searchModal) {
        return;
    }
    
    let searchTimeout;

    // Use your actual production Content API key
    const apiKey = 'eb70f37cae7b7de2fd17aa22d1';

    if (!apiKey) {
        console.error('Content API key not configured');
        searchResults.innerHTML = '<div class="page-search-no-results">Search unavailable</div>';
        return;
    }

    // Open modal
    searchTrigger.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        searchModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        setTimeout(() => searchField.focus(), 100);
    });

    // Close modal
    function closeModal() {
        searchModal.style.display = 'none';
        document.body.style.overflow = '';
        searchField.value = '';
        searchResults.innerHTML = '';
    }

    if (searchClose) {
        searchClose.addEventListener('click', closeModal);
    }
    
    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', closeModal);
    }

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchModal.style.display === 'block') {
            closeModal();
        }
    });

    function performSearch(query) {
        if (query.length < 2) {
            searchResults.innerHTML = '';
            return;
        }

        // Make parallel API calls for both posts and pages
        const pagesUrl = `/ghost/api/content/pages/?key=${apiKey}&fields=title,url,excerpt,plaintext,html&limit=all`;
        const postsUrl = `/ghost/api/content/posts/?key=${apiKey}&fields=title,url,excerpt,plaintext,html&limit=all`;
        
        Promise.all([
            fetch(pagesUrl).then(response => {
                if (!response.ok) {
                    throw new Error(`Pages API error! status: ${response.status}`);
                }
                return response.json();
            }),
            fetch(postsUrl).then(response => {
                if (!response.ok) {
                    throw new Error(`Posts API error! status: ${response.status}`);
                }
                return response.json();
            })
        ])
        .then(([pagesData, postsData]) => {
            if (!pagesData.pages || !postsData.posts) {
                throw new Error('No data in API response');
            }
            
            // Combine pages and posts into one array
            const allContent = [
                ...pagesData.pages.map(page => ({...page, type: 'page'})),
                ...postsData.posts.map(post => ({...post, type: 'post'}))
            ];
            
            // Filter combined results
            const results = allContent.filter(item => {
                const searchText = query.toLowerCase();
                
                // Search in title, excerpt, plaintext, and raw HTML content
                return item.title.toLowerCase().includes(searchText) ||
                       (item.excerpt && item.excerpt.toLowerCase().includes(searchText)) ||
                       (item.plaintext && item.plaintext.toLowerCase().includes(searchText)) ||
                       (item.html && item.html.toLowerCase().includes(searchText));
            });

            displayResults(results, query);
        })
        .catch(error => {
            console.error('Search error:', error);
            searchResults.innerHTML = '<div class="page-search-no-results">Search temporarily unavailable</div>';
        });
    }

    function displayResults(results, query) {
        if (results.length === 0) {
            searchResults.innerHTML = '<div class="page-search-no-results">No pages found</div>';
        } else {
            const resultsHtml = results.map(page => {
                let excerpt = '';
                if (page.excerpt) {
                    excerpt = page.excerpt.length > 160 ? 
                        page.excerpt.substring(0, 160) + '...' : 
                        page.excerpt;
                } else if (page.plaintext) {
                    excerpt = page.plaintext.length > 160 ? 
                        page.plaintext.substring(0, 160) + '...' : 
                        page.plaintext;
                }
                
                return `
                    <div class="page-search-result">
                        <a href="${page.url}" class="page-search-result-link">
                            <h4 class="page-search-result-title">${highlightText(page.title, query)}</h4>
                            ${excerpt ? `<p class="page-search-result-excerpt">${highlightText(excerpt, query)}</p>` : ''}
                        </a>
                    </div>
                `;
            }).join('');
            searchResults.innerHTML = resultsHtml;
        }
    }

    function highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    if (searchField) {
        searchField.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
    }

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch(searchField.value.trim());
        });
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageSearch);
} else {
    initPageSearch();
}
</script>
