jQuery.noConflict();

(function ($) {
    var image = $('.jarallax-img');
    if (!image) return;

    var options = {
        disableParallax: /iPad|iPhone|iPod|Android/,
        disableVideo: /iPad|iPhone|iPod|Android/,
        speed: 0.1,
    };

    image.imagesLoaded(function () {
        image.parent().jarallax(options).addClass('initialized');
    });
})(jQuery);

(function ($) {
    'use strict';
    $('.featured-posts').owlCarousel({
        dots: false,
        margin: 30,
        nav: true,
        navText: [
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M20.547 22.107L14.44 16l6.107-6.12L18.667 8l-8 8 8 8 1.88-1.893z"></path></svg>',
            '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M11.453 22.107L17.56 16l-6.107-6.12L13.333 8l8 8-8 8-1.88-1.893z"></path></svg>',
        ],
        responsive: {
            0: {
                items: 1,
                slideBy: 1,
            },
            768: {
                items: 3,
                slideBy: 3,
            },
            992: {
                items: 4,
                slideBy: 4,
            },
        },
    });
})(jQuery);

// Mouse parallax effect for background
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    // Calculate offset (-10% to +10%)
    const offsetX = (mouseX - 0.5) * 100; // 20% range centered at 0
    const offsetY = (mouseY - 0.5) * 100;
    
    // Apply to body background
    document.body.style.backgroundPosition = `${50 + offsetX}% ${50 + offsetY}%`;
});


(function ($) {
    // Conditionally load Owl Carousel if .owl-carousel elements exist
    if ($('.owl-carousel').length > 0) {
        // Load Owl Carousel JavaScript
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js';
        script.onload = function() {
            // Randomize slide order before initializing carousel
            $('.owl-carousel').each(function() {
                var $carousel = $(this);
                var $slides = $carousel.children();

                // Fisher-Yates shuffle algorithm
                for (var i = $slides.length - 1; i > 0; i--) {
                    var j = Math.floor(Math.random() * (i + 1));
                    $carousel.append($slides.eq(j));
                }
            });

            // Initialize Owl Carousel after randomizing slides
            $('.owl-carousel').owlCarousel({
                loop:true,
                margin:-5,
                stagePadding: 80,
                responsiveClass:true,
                navText: ["<span class='icon icon-arrow-left7'>←</span>","<span class='icon icon-arrow-right7'>→</span>"],
                responsive:{
                    0:{
                        items:1,
                        nav:true,  
                        loop: true,
                        margin:12,
                        stagePadding: 0,
                    },
                    600:{
                        items:1,
                        nav:true,  
                        loop: true,
                        margin:12,
                        stagePadding: 0,
                    },
                    1000:{
                        margin:-5,
                        stagePadding: 80,
                        items:1,
                        nav:true,
                        loop:false
                    }
                }
            });
        };
        document.head.appendChild(script);
    }
})(jQuery);

// Random Gradient Background Generator
// Usage: Add class "random-gradient" to any element or call generateRandomGradient(element) directly
function generateRandomGradient(element, options = {}) {
    const defaults = {
        type: 'linear', // 'linear', 'radial', or 'conic'
        direction: null, // null for random diagonal, or specify like '45deg', 'to right', etc.
        colorCount: 3, // number of colors in gradient (2-5)
        opacity: 1, // opacity of the gradient
        saturation: 60, // saturation percentage (0-100) - higher for rich dark colors
        lightness: 25, // lightness percentage (0-100) - lower for dark colors
        hueRange: [0, 360], // hue range [min, max]
        blendMode: null // CSS blend mode like 'multiply', 'overlay', etc.
    };

    const config = { ...defaults, ...options };

    // Generate random dark colors
    function generateRandomColor() {
        const hue = Math.floor(Math.random() * (config.hueRange[1] - config.hueRange[0]) + config.hueRange[0]);
        // Keep saturation moderate to high (40-80%) for rich dark colors
        const saturation = Math.max(40, Math.min(80, config.saturation + (Math.random() * 20 - 10)));
        // Keep lightness low (10-40%) for dark effect
        const lightness = Math.max(10, Math.min(40, config.lightness + (Math.random() * 20 - 10)));
        return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    }

    // Generate gradient direction - only diagonal angles
    function getDirection() {
        if (config.direction) return config.direction;

        switch (config.type) {
            case 'linear':
                // Only diagonal angles for linear gradients
                const diagonalAngles = ['45deg', '135deg', '225deg', '315deg'];
                const diagonalDirections = ['to top right', 'to bottom right', 'to bottom left', 'to top left'];
                return Math.random() > 0.5 ?
                    diagonalAngles[Math.floor(Math.random() * diagonalAngles.length)] :
                    diagonalDirections[Math.floor(Math.random() * diagonalDirections.length)];
            case 'radial':
                const shapes = ['circle', 'ellipse'];
                const positions = ['center', 'top left', 'top right', 'bottom left', 'bottom right'];
                return `${shapes[Math.floor(Math.random() * shapes.length)]} at ${positions[Math.floor(Math.random() * positions.length)]}`;
            case 'conic':
                // Use diagonal starting points for conic gradients
                const diagonalStarts = [45, 135, 225, 315];
                return `from ${diagonalStarts[Math.floor(Math.random() * diagonalStarts.length)]}deg`;
            default:
                return '45deg';
        }
    }

    // Generate colors array
    const colors = [];
    for (let i = 0; i < config.colorCount; i++) {
        const color = generateRandomColor();
        const position = i === 0 ? '0%' : i === config.colorCount - 1 ? '100%' : `${Math.floor((i / (config.colorCount - 1)) * 100)}%`;
        colors.push(`${color} ${position}`);
    }

    // Create gradient string
    const direction = getDirection();
    let gradient;

    switch (config.type) {
        case 'radial':
            gradient = `radial-gradient(${direction}, ${colors.join(', ')})`;
            break;
        case 'conic':
            gradient = `conic-gradient(${direction}, ${colors.join(', ')})`;
            break;
        default:
            gradient = `linear-gradient(${direction}, ${colors.join(', ')})`;
    }

    // Apply gradient to element
    if (element) {
        element.style.background = gradient;
        if (config.opacity < 1) {
            element.style.opacity = config.opacity;
        }
        if (config.blendMode) {
            element.style.mixBlendMode = config.blendMode;
        }

        // Store gradient info as data attribute for potential reuse
        element.setAttribute('data-gradient', gradient);
        element.setAttribute('data-gradient-config', JSON.stringify(config));
    }

    return gradient;
}

// Auto-initialize elements with random-gradient class
function initRandomGradients() {
    const elements = document.querySelectorAll('.random-gradient');
    elements.forEach(element => {
        // Check for custom options in data attributes
        const options = {};

        if (element.dataset.gradientType) options.type = element.dataset.gradientType;
        if (element.dataset.gradientDirection) options.direction = element.dataset.gradientDirection;
        if (element.dataset.gradientColors) options.colorCount = parseInt(element.dataset.gradientColors);
        if (element.dataset.gradientOpacity) options.opacity = parseFloat(element.dataset.gradientOpacity);
        if (element.dataset.gradientSaturation) options.saturation = parseInt(element.dataset.gradientSaturation);
        if (element.dataset.gradientLightness) options.lightness = parseInt(element.dataset.gradientLightness);
        if (element.dataset.gradientHueMin && element.dataset.gradientHueMax) {
            options.hueRange = [parseInt(element.dataset.gradientHueMin), parseInt(element.dataset.gradientHueMax)];
        }
        if (element.dataset.gradientBlend) options.blendMode = element.dataset.gradientBlend;

        generateRandomGradient(element, options);
    });
}

// Utility function to regenerate gradient for a specific element
function regenerateGradient(element, newOptions = {}) {
    const existingConfig = element.getAttribute('data-gradient-config');
    const currentOptions = existingConfig ? JSON.parse(existingConfig) : {};
    const options = { ...currentOptions, ...newOptions };
    return generateRandomGradient(element, options);
}

// Initialize on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initRandomGradients);
} else {
    initRandomGradients();
}

// Make functions globally available
window.generateRandomGradient = generateRandomGradient;
window.regenerateGradient = regenerateGradient;
window.initRandomGradients = initRandomGradients;