/**
 * View Transitions Support for Theme
 * Provides smooth page transitions using the View Transitions API
 */

(function() {
    'use strict';

    // Check if view transitions are supported
    const supportsViewTransitions = 'startViewTransition' in document;

    // Configuration
    const config = {
        // Selectors for elements that should trigger view transitions
        linkSelectors: [
            'a[href^="/"]',           // Internal links
            'a[href^="' + window.location.origin + '"]', // Same origin links
            '.gh-head-menu a',        // Navigation links
            '.gh-foot-menu a',        // Footer links
            '.pagination a',          // Pagination links
            '.post-card-link'         // Post card links
        ].join(', '),
        
        // Elements to exclude from view transitions
        excludeSelectors: [
            'a[href*="#"]',           // Anchor links
            'a[target="_blank"]',     // External links
            'a[download]',            // Download links
            'a[href^="mailto:"]',     // Email links
            'a[href^="tel:"]',        // Phone links
            'a[data-no-transition]'   // Explicitly excluded
        ].join(', '),

        // Transition duration (should match CSS)
        duration: 400
    };

    /**
     * Check if a link should use view transitions
     */
    function shouldTransition(link) {
        // Skip if view transitions not supported
        if (!supportsViewTransitions) return false;

        // Skip if link matches exclude selectors
        if (link.matches(config.excludeSelectors)) return false;

        // Skip if link has special attributes
        if (link.hasAttribute('data-no-transition') || 
            link.hasAttribute('download') ||
            link.target === '_blank') return false;

        // Skip if it's not an internal link
        const href = link.getAttribute('href');
        if (!href || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) {
            return false;
        }

        // Check if it's a same-origin link
        try {
            const url = new URL(href, window.location.origin);
            return url.origin === window.location.origin;
        } catch (e) {
            return false;
        }
    }

    /**
     * Determine navigation direction
     */
    function getNavigationDirection(currentUrl, targetUrl) {
        // Simple heuristic: if going to a post from index, it's forward
        // if going back to index from post, it's back
        const current = new URL(currentUrl, window.location.origin);
        const target = new URL(targetUrl, window.location.origin);

        // Check if navigating from index to post (forward)
        if (current.pathname === '/' && target.pathname !== '/') {
            return 'forward';
        }

        // Check if navigating from post to index (back)
        if (current.pathname !== '/' && target.pathname === '/') {
            return 'back';
        }

        // Check pagination direction
        if (current.pathname.includes('/page/') || target.pathname.includes('/page/')) {
            const currentPage = parseInt(current.pathname.match(/\/page\/(\d+)/)?.[1] || '1');
            const targetPage = parseInt(target.pathname.match(/\/page\/(\d+)/)?.[1] || '1');

            if (targetPage > currentPage) return 'forward';
            if (targetPage < currentPage) return 'back';
        }

        return 'forward'; // Default
    }

    /**
     * Navigate with view transition
     */
    function navigateWithTransition(url, direction = null) {
        if (!supportsViewTransitions) {
            window.location.href = url;
            return;
        }

        // Add loading state
        document.body.classList.add('view-transition-loading');

        // Determine direction if not provided
        if (!direction) {
            direction = getNavigationDirection(window.location.href, url);
        }

        // Add direction class to body
        document.body.classList.add(`view-transition-${direction}`);

        // Start view transition
        const transition = document.startViewTransition(() => {
            return fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // Parse the new page
                    const parser = new DOMParser();
                    const newDoc = parser.parseFromString(html, 'text/html');

                    // Update the page title
                    document.title = newDoc.title;

                    // Update the main content
                    const newContent = newDoc.querySelector('.site-content');
                    const currentContent = document.querySelector('.site-content');

                    if (newContent && currentContent) {
                        currentContent.innerHTML = newContent.innerHTML;
                    }

                    // Update body classes
                    const newBodyClasses = newDoc.body.className;
                    document.body.className = newBodyClasses + ' view-transitions-supported';

                    // Update meta tags
                    updateMetaTags(newDoc);

                    // Update the URL
                    history.pushState({ direction: direction }, '', url);

                    // Re-initialize any JavaScript components
                    reinitializeComponents();
                })
                .catch(error => {
                    console.warn('View transition failed, falling back to normal navigation:', error);
                    window.location.href = url;
                })
                .finally(() => {
                    // Remove loading state
                    document.body.classList.remove('view-transition-loading');
                    document.body.classList.remove(`view-transition-${direction}`);
                });
        });

        // Handle transition errors
        transition.catch(error => {
            console.warn('View transition error:', error);
            document.body.classList.remove('view-transition-loading');
            document.body.classList.remove(`view-transition-${direction}`);
        });
    }

    /**
     * Update meta tags from new document
     */
    function updateMetaTags(newDoc) {
        // Update description
        const newDescription = newDoc.querySelector('meta[name="description"]');
        const currentDescription = document.querySelector('meta[name="description"]');
        if (newDescription && currentDescription) {
            currentDescription.setAttribute('content', newDescription.getAttribute('content'));
        }

        // Update keywords
        const newKeywords = newDoc.querySelector('meta[name="keywords"]');
        const currentKeywords = document.querySelector('meta[name="keywords"]');
        if (newKeywords && currentKeywords) {
            currentKeywords.setAttribute('content', newKeywords.getAttribute('content'));
        }

        // Update canonical URL
        const newCanonical = newDoc.querySelector('link[rel="canonical"]');
        const currentCanonical = document.querySelector('link[rel="canonical"]');
        if (newCanonical && currentCanonical) {
            currentCanonical.setAttribute('href', newCanonical.getAttribute('href'));
        }
    }

    /**
     * Re-initialize JavaScript components after transition
     */
    function reinitializeComponents() {
        // Re-attach event listeners
        attachEventListeners();
        
        // Trigger custom event for other scripts
        document.dispatchEvent(new CustomEvent('viewTransitionComplete', {
            detail: { url: window.location.href }
        }));

        // Re-initialize any jQuery components if needed
        if (window.jQuery) {
            // Re-initialize search if present
            if (window.jQuery('.page-search-trigger').length) {
                // Search initialization would go here
            }
            
            // Re-initialize any carousels
            if (window.jQuery('.owl-carousel').length) {
                // Carousel re-initialization would go here
            }
        }
    }

    /**
     * Attach event listeners to links
     */
    function attachEventListeners() {
        // Remove existing listeners to prevent duplicates
        document.removeEventListener('click', handleLinkClick);
        
        // Add click listener
        document.addEventListener('click', handleLinkClick);
    }

    /**
     * Handle link clicks
     */
    function handleLinkClick(event) {
        const link = event.target.closest('a');
        
        if (!link || !shouldTransition(link)) {
            return;
        }

        // Prevent default navigation
        event.preventDefault();
        
        const href = link.getAttribute('href');
        if (href && href !== window.location.pathname) {
            navigateWithTransition(href);
        }
    }

    /**
     * Handle browser back/forward buttons
     */
    function handlePopState() {
        if (supportsViewTransitions) {
            const direction = history.state?.direction === 'forward' ? 'back' : 'forward';
            navigateWithTransition(window.location.href, direction);
        }
    }

    /**
     * Initialize view transitions
     */
    function init() {
        if (!supportsViewTransitions) {
            console.log('View Transitions API not supported, using standard navigation');
            return;
        }

        console.log('View Transitions API supported, enabling smooth navigation');
        
        // Attach event listeners
        attachEventListeners();
        
        // Handle browser navigation
        window.addEventListener('popstate', handlePopState);
        
        // Add class to body to indicate support
        document.body.classList.add('view-transitions-supported');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Expose API for other scripts
    if (typeof window !== 'undefined') {
        window['ViewTransitions'] = {
            navigate: navigateWithTransition,
            isSupported: () => supportsViewTransitions,
            config: config
        };
    }

})();
