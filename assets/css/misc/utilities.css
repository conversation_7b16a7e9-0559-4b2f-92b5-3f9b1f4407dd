.u-object-fit {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.u-permalink {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 50;
    outline: none;
}

.u-placeholder {
    position: relative;
    z-index: 10;
    background-color: var(--light-gray-color);
}

.u-placeholder.same-height {
    height: 0;
}

.u-placeholder.horizontal {
    padding-bottom: 50%;
}

.u-placeholder.rectangle {
    padding-bottom: 62.5%;
}

.u-placeholder.square {
    padding-bottom: 100%;
}

.u-overlay {
    position: relative;
}

.u-overlay::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    content: "";
    background-color: var(--black-color);
    transition: opacity 1s var(--animation-base);
}

.no-image.u-overlay::before {
    background-color: var(--ghost-accent-color);
}

.u-overlay:not(.no-image).initialized::before {
    opacity: 0.4;
}

@media (max-width: 575px) {
    .hidden-xs {
        display: none !important;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .hidden-sm {
        display: none !important;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .hidden-md {
        display: none !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .hidden-lg {
        display: none !important;
    }
}

@media (min-width: 1200px) {
    .hidden-xl {
        display: none !important;
    }
}
