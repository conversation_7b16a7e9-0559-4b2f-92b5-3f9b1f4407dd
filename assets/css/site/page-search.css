/* Search trigger button - matches Ghost search button */
.page-search-trigger {
    /* Inherits styling from .gh-search .gh-icon-btn */
}

/* Modal styles */
.page-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.page-search-modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
}

.page-search-modal-content {
    position: relative;
    max-width: 600px;
    margin: 80px auto 0;
    background: var(--white-color);
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.page-search-modal-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--light-gray-color);
}

.page-search-form {
    flex: 1;
    margin-right: 15px;
}

.form-wrapper {
    display: flex;
    align-items: center;
    background: var(--light-gray-color);
    border-radius: 5px;
    padding: 0px;
    height: 50px;
}

.page-search-field {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    color: var(--dark-gray-color);
}

.page-search-field:focus {
    outline: none;
    box-shadow: none;
}

.page-search-button {
    border: none;
    background: transparent;
    color: var(--dark-gray-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 0;
    padding-left: 15px;
}

.page-search-close {
    border: none;
    background: transparent;
    color: var(--dark-gray-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
}

.page-search-close:hover {
    background: var(--light-gray-color);
}

.page-search-results {
    max-height: 400px;
    overflow-y: auto;
    background: var(--white-color);
}

.page-search-result {
    border-bottom: 1px solid var(--light-gray-color);
    background: var(--white-color);
}

.page-search-result:last-child {
    border-bottom: none;
}

.page-search-result-link {
    display: block;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    background: var(--white-color);
}

.page-search-result-link:hover {
    background: var(--light-gray-color);
}

.page-search-result-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-gray-color);
}

.page-search-result-excerpt {
    margin: 0;
    font-size: 14px;
    color: var(--mid-gray-color);
    line-height: 1.5;
}

.page-search-no-results {
    padding: 40px 20px;
    text-align: center;
    color: var(--mid-gray-color);
    font-style: italic;
    background: var(--white-color);
}

.page-search mark {
    background: var(--ghost-accent-color);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
}

.page-search-modal input[type=text]:focus {
    border-color: rgba(255, 255, 255, 0.5);
}

/* Dark mode */
.darkMode .page-search-modal-content {
    background: var(--white-color);
}


.darkMode .page-search-modal input[type=text]:focus {
    border-color: rgba(0, 0, 0, 0.5);
}

.darkMode .page-search-modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
    background: var(--white-color);
}

.darkMode .form-wrapper {
    background: rgba(255, 255, 255, 0.1);
}

.darkMode .page-search-field {
    color: #000;
}

.darkMode .page-search-button,
.darkMode .page-search-close {
    color:  #000;
}

.darkMode .page-search-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.darkMode .page-search-results {
    background: var(--white-color);
}

.darkMode .page-search-result {
    border-bottom-color: rgba(255, 255, 255, 0.1);
    background: #fff;
}

.darkMode .page-search-result-link {
    background:  #fff;
}

.darkMode .page-search-result-link:hover {
    background: rgba(255, 255, 255, 0.466);
}

.darkMode .page-search-result-title {
    color: #000;
}

.darkMode .page-search-result-excerpt {
    color: #000;
}

.darkMode .page-search-no-results {
    background: var(--dark-color);
}
