# View Transitions Support

This theme now includes support for the modern **View Transitions API** which provides smooth, animated transitions between pages without requiring a full page reload.

## Features

### ✨ Smooth Page Transitions
- Automatic smooth transitions between pages
- Different animation directions based on navigation (forward/back)
- Maintains header and footer elements during transitions
- Graceful fallback for browsers without support

### 🎯 Smart Navigation Detection
- Detects navigation direction (forward/back)
- <PERSON>les pagination transitions
- Supports browser back/forward buttons
- Preserves scroll position where appropriate

### 🎨 Customizable Animations
- Slide transitions for main content
- Fade effects for headers and titles
- Scale animations for article elements
- Respects user's motion preferences

## Browser Support

View Transitions are supported in:
- Chrome 111+
- Edge 111+
- Opera 97+

For unsupported browsers, the theme falls back to standard navigation.

## How It Works

### Automatic Enhancement
The view transitions are automatically applied to:
- Navigation menu links
- Post/page links
- Pagination links
- Footer links

### Excluded Elements
The following links will use standard navigation:
- External links (`target="_blank"`)
- Anchor links (`href="#section"`)
- Download links (`download` attribute)
- Email/phone links (`mailto:`, `tel:`)
- Links with `data-no-transition` attribute

### CSS Classes
The theme adds these CSS classes during transitions:
- `.view-transitions-supported` - Added to body when supported
- `.view-transition-loading` - Added during transition loading
- `.view-transition-forward` - Added for forward navigation
- `.view-transition-back` - Added for backward navigation

## Customization

### Disabling for Specific Links
Add the `data-no-transition` attribute to any link:
```html
<a href="/page" data-no-transition>Standard Navigation</a>
```

### Custom Animations
You can customize the animations by modifying the CSS in `assets/css/general/styles.css`:

```css
/* Custom transition for specific elements */
.my-element {
  view-transition-name: my-custom-element;
}

::view-transition-old(my-custom-element) {
  animation: my-custom-out 0.3s ease-out;
}

::view-transition-new(my-custom-element) {
  animation: my-custom-in 0.3s ease-in;
}
```

### JavaScript API
Access the view transitions programmatically:

```javascript
// Check if supported
if (window.ViewTransitions?.isSupported()) {
  // Navigate with custom transition
  window.ViewTransitions.navigate('/target-page');
}

// Listen for transition completion
document.addEventListener('viewTransitionComplete', (event) => {
  console.log('Transitioned to:', event.detail.url);
});
```

## Performance

- Transitions are hardware-accelerated when possible
- Minimal JavaScript overhead
- Respects `prefers-reduced-motion` setting
- Graceful degradation for older browsers

## Accessibility

- Respects user's motion preferences
- Maintains focus management
- Preserves screen reader navigation
- No impact on keyboard navigation

## Troubleshooting

### Transitions Not Working
1. Check browser support (Chrome 111+, Edge 111+, Opera 97+)
2. Ensure JavaScript is enabled
3. Check for console errors
4. Verify the link doesn't have exclusion attributes

### Performance Issues
1. Reduce animation duration in CSS
2. Simplify transition effects
3. Check for conflicting CSS animations

### Debugging
Enable debug logging by adding to console:
```javascript
// Enable debug mode
window.ViewTransitions.config.debug = true;
```

## Files Modified

- `assets/css/general/styles.css` - View transition CSS rules
- `assets/js/view-transitions.js` - JavaScript implementation
- `default.hbs` - Script inclusion
- `assets/js/main.js` - Integration setup

## Future Enhancements

Potential improvements for future versions:
- Shared element transitions for images
- Custom transition types per page template
- Preloading for faster transitions
- Advanced gesture support
